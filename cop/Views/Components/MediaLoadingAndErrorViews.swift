//
//  MediaLoadingAndErrorViews.swift
//  cop
//
//  Created by 阿亮 on 2025/6/1.
//

import SwiftUI

// MARK: - 加载视图
struct MediaLoadingView: View {
    let message: String
    
    init(message: String = "正在加载...") {
        self.message = message
    }
    
    var body: some View {
        VStack(spacing: 24) {
            ProgressView()
                .progressViewStyle(CircularProgressViewStyle(tint: .white))
                .scaleEffect(1.3)

            Text(message)
                .foregroundColor(.white.opacity(0.8))
                .font(.system(size: 18, weight: .medium))
        }
    }
}

// MARK: - 图片加载视图
struct ImageLoadingView: View {
    var body: some View {
        VStack(spacing: 20) {
            ProgressView()
                .progressViewStyle(CircularProgressViewStyle(tint: .white))
                .scaleEffect(1.2)

            Text("加载中...")
                .foregroundColor(.white.opacity(0.8))
                .font(.system(size: 17, weight: .medium))
        }
    }
}



// MARK: - 图片错误视图
struct ImageErrorView: View {
    let onRetry: () -> Void
    
    var body: some View {
        VStack(spacing: 20) {
            Image(systemName: "photo")
                .font(.system(size: 48))
                .foregroundColor(.white.opacity(0.6))

            Text("无法加载图片")
                .foregroundColor(.white.opacity(0.8))
                .font(.system(size: 17, weight: .medium))

            Button("重新加载") {
                onRetry()
            }
            .foregroundColor(.white)
            .font(.system(size: 16, weight: .medium))
            .padding(.horizontal, 20)
            .padding(.vertical, 10)
            .background(.blue.opacity(0.8), in: Capsule())
        }
    }
}


