//
//  MonitoringDashboardView.swift
//  cop
//
//  Created by Augment Agent on 2025/1/2.
//

import SwiftUI
import Charts

// MARK: - 统一监测仪表板
struct MonitoringDashboardView: View {
    @StateObject private var browserManager = BrowserManager.shared
    @StateObject private var memoryManager = UnifiedMemoryManager.shared
    @StateObject private var serviceManager = ServiceManager.shared
    @StateObject private var securityService = SecurityService.shared
    @State private var selectedTimeRange: TimeRange = .hour
    @State private var refreshTrigger = false
    
    enum TimeRange: String, CaseIterable {
        case hour = "最近1小时"
        case day = "最近24小时"
        case week = "最近7天"
        
        var interval: TimeInterval {
            switch self {
            case .hour: return 3600
            case .day: return 86400
            case .week: return 604800
            }
        }
    }
    
    var body: some View {
        NavigationView {
            ScrollView {
                LazyVStack(spacing: 20) {
                    // 时间范围选择器
                    timeRangePicker
                    
                    // 概览卡片
                    overviewCards
                    
                    // 性能监控图表
                    performanceCharts
                    
                    // 内存管理详情
                    memoryManagementSection
                    
                    // 服务状态监控
                    servicesStatusSection
                    
                    // 安全监控
                    securityMonitoringSection
                    
                    // 系统信息
                    systemInfoSection
                }
                .padding()
            }
            .navigationTitle("系统监测仪表板")
            .navigationBarTitleDisplayMode(.large)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("刷新") {
                        refreshData()
                    }
                }
            }
            .onAppear {
                startMonitoring()
            }
            .onDisappear {
                stopMonitoring()
            }
        }
    }
    
    // MARK: - 时间范围选择器
    private var timeRangePicker: some View {
        Picker("时间范围", selection: $selectedTimeRange) {
            ForEach(TimeRange.allCases, id: \.self) { range in
                Text(range.rawValue).tag(range)
            }
        }
        .pickerStyle(SegmentedPickerStyle())
        .padding(.horizontal)
    }
    
    // MARK: - 概览卡片
    private var overviewCards: some View {
        LazyVGrid(columns: [
            GridItem(.flexible()),
            GridItem(.flexible())
        ], spacing: 16) {
            // 内存使用率
            OverviewCard(
                title: "内存使用率",
                                    value: "\(Int(memoryManager.currentMemoryState.utilizationRatio * 100))%",
                    color: memoryManager.currentMemoryState.pressureLevel == .critical ? .red : 
                           memoryManager.currentMemoryState.pressureLevel == .warning ? .orange : .green,
                icon: "memorychip"
            )
            
            // CPU使用率（简化指标）
            OverviewCard(
                title: "CPU使用率",
                value: "正常",
                color: .blue,
                icon: "cpu"
            )
            
            // 活跃WebView数量
            OverviewCard(
                title: "活跃标签页",
                value: "\(browserManager.activeWebViews.count)",
                color: browserManager.activeWebViews.count > 10 ? .yellow : .cyan,
                icon: "globe"
            )
            
            // 内存使用量
            OverviewCard(
                title: "内存使用量",
                value: ByteCountFormatter.string(fromByteCount: Int64(browserManager.memoryUsage), countStyle: .memory),
                color: browserManager.memoryUsage > 200_000_000 ? .red : .green,
                icon: "timer"
            )
        }
    }
    
    // MARK: - 性能监控图表
    private var performanceCharts: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("性能趋势")
                .font(.headline)
                .fontWeight(.semibold)
            
            // 内存使用趋势图
            ChartCardView(
                title: "内存使用趋势",
                icon: "chart.line.uptrend.xyaxis"
            ) {
                // 这里可以添加真实的图表数据
                Text("内存使用趋势图")
                    .foregroundColor(.secondary)
                    .frame(height: 120)
            }
            
            // CPU使用趋势图
            ChartCardView(
                title: "CPU使用趋势",
                icon: "chart.bar.fill"
            ) {
                Text("CPU使用趋势图")
                    .foregroundColor(.secondary)
                    .frame(height: 120)
            }
        }
    }
    
    // MARK: - 内存管理详情
    private var memoryManagementSection: some View {
        ExpandableSection(
            title: "内存管理详情",
            icon: "memorychip.fill"
        ) {
            VStack(alignment: .leading, spacing: 12) {
                MemoryDetailRow(
                    label: "当前使用",
                    value: ByteCountFormatter.string(fromByteCount: Int64(memoryManager.currentMemoryState.currentUsage), countStyle: .memory)
                )
                
                MemoryDetailRow(
                    label: "可用内存",
                    value: ByteCountFormatter.string(fromByteCount: Int64(memoryManager.currentMemoryState.availableMemory), countStyle: .memory)
                )
                
                MemoryDetailRow(
                    label: "总内存",
                    value: ByteCountFormatter.string(fromByteCount: Int64(memoryManager.currentMemoryState.totalMemory), countStyle: .memory)
                )
                
                MemoryDetailRow(
                    label: "WebView缓存",
                    value: ByteCountFormatter.string(fromByteCount: Int64(memoryManager.currentMemoryState.webViewMemory), countStyle: .memory)
                )
                
                MemoryDetailRow(
                    label: "压力等级",
                    value: memoryManager.currentMemoryState.pressureLevel.description
                )
            }
        }
    }
    
    // MARK: - 服务状态监控
    private var servicesStatusSection: some View {
        ExpandableSection(
            title: "服务状态监控",
            icon: "gear.circle.fill"
        ) {
            LazyVStack(spacing: 8) {
                ForEach(Array(serviceManager.services.values), id: \.name) { service in
                    ServiceStatusRow(service: service)
                }
            }
        }
    }
    
    // MARK: - 安全监控
    private var securityMonitoringSection: some View {
        ExpandableSection(
            title: "安全监控",
            icon: "shield.fill"
        ) {
            VStack(alignment: .leading, spacing: 12) {
                SecurityMetricRow(
                    label: "威胁检测",
                    value: "\(securityService.statistics.totalThreatsDetected)",
                    icon: "exclamationmark.triangle.fill",
                    color: .red
                )
                
                SecurityMetricRow(
                    label: "已阻止请求",
                    value: "\(securityService.statistics.httpBlockedRequests)",
                    icon: "hand.raised.fill",
                    color: .orange
                )
                
                SecurityMetricRow(
                    label: "安全扫描",
                    value: "\(securityService.statistics.totalBlockedItems)",
                    icon: "magnifyingglass.circle.fill",
                    color: .blue
                )
                
                SecurityMetricRow(
                    label: "隐私保护",
                    value: "\(securityService.statistics.trackersBlocked)",
                    icon: "lock.shield.fill",
                    color: .green
                )
            }
        }
    }
    
    // MARK: - 系统信息
    private var systemInfoSection: some View {
        ExpandableSection(
            title: "系统信息",
            icon: "info.circle.fill"
        ) {
            VStack(alignment: .leading, spacing: 12) {
                SystemInfoRow(label: "设备型号", value: UIDevice.current.model)
                SystemInfoRow(label: "系统版本", value: UIDevice.current.systemVersion)
                SystemInfoRow(label: "应用版本", value: Bundle.main.infoDictionary?["CFBundleShortVersionString"] as? String ?? "未知")
                SystemInfoRow(label: "构建版本", value: Bundle.main.infoDictionary?["CFBundleVersion"] as? String ?? "未知")
            }
        }
    }
    
    // MARK: - 私有方法
    private func startMonitoring() {
        // 简化的监控启动 - 浏览器管理器已经自动处理监控
        print("🔄 监控仪表板开始运行")
        // 其他监控服务已经在应用启动时开始
    }
    
    private func stopMonitoring() {
        // 保持监控运行，只是停止UI更新
    }
    
    private func refreshData() {
        refreshTrigger.toggle()
        // 触发数据刷新
    }
}

// MARK: - 辅助视图组件

struct OverviewCard: View {
    let title: String
    let value: String
    let color: Color
    let icon: String
    
    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            HStack {
                Image(systemName: icon)
                    .foregroundColor(color)
                Spacer()
            }
            
            Text(value)
                .font(.title2)
                .fontWeight(.bold)
                .foregroundColor(color)
            
            Text(title)
                .font(.caption)
                .foregroundColor(.secondary)
        }
        .padding()
        .background(Color(UIColor.secondarySystemBackground))
        .cornerRadius(12)
    }
}

struct ChartCardView<Content: View>: View {
    let title: String
    let icon: String
    let content: Content
    
    init(title: String, icon: String, @ViewBuilder content: () -> Content) {
        self.title = title
        self.icon = icon
        self.content = content()
    }
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                Image(systemName: icon)
                    .foregroundColor(.blue)
                Text(title)
                    .font(.subheadline)
                    .fontWeight(.medium)
                Spacer()
            }
            
            content
        }
        .padding()
        .background(Color(UIColor.secondarySystemBackground))
        .cornerRadius(12)
    }
}

struct ExpandableSection<Content: View>: View {
    let title: String
    let icon: String
    let content: Content
    @State private var isExpanded = true
    
    init(title: String, icon: String, @ViewBuilder content: () -> Content) {
        self.title = title
        self.icon = icon
        self.content = content()
    }
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            Button(action: {
                withAnimation(.easeInOut) {
                    isExpanded.toggle()
                }
            }) {
                HStack {
                    Image(systemName: icon)
                        .foregroundColor(.blue)
                    Text(title)
                        .font(.headline)
                        .fontWeight(.semibold)
                    Spacer()
                    Image(systemName: isExpanded ? "chevron.up" : "chevron.down")
                        .foregroundColor(.secondary)
                }
            }
            .buttonStyle(PlainButtonStyle())
            
            if isExpanded {
                content
            }
        }
        .padding()
        .background(Color(UIColor.secondarySystemBackground))
        .cornerRadius(12)
    }
}

struct MemoryDetailRow: View {
    let label: String
    let value: String
    
    var body: some View {
        HStack {
            Text(label)
                .foregroundColor(.secondary)
            Spacer()
            Text(value)
                .fontWeight(.medium)
        }
    }
}

struct ServiceStatusRow: View {
    let service: ServiceInfo
    
    var body: some View {
        HStack {
            Circle()
                .fill(service.isRunning ? Color.green : Color.red)
                .frame(width: 8, height: 8)
            
            Text(service.name)
                .font(.subheadline)
            
            Spacer()
            
            Text("v\(service.version)")
                .font(.caption)
                .foregroundColor(.secondary)
            
            Text("\(service.memoryUsage)KB")
                .font(.caption)
                .foregroundColor(.secondary)
        }
    }
}

struct SecurityMetricRow: View {
    let label: String
    let value: String
    let icon: String
    let color: Color
    
    var body: some View {
        HStack {
            Image(systemName: icon)
                .foregroundColor(color)
                .frame(width: 20)
            
            Text(label)
                .foregroundColor(.secondary)
            
            Spacer()
            
            Text(value)
                .fontWeight(.medium)
        }
    }
}

struct SystemInfoRow: View {
    let label: String
    let value: String
    
    var body: some View {
        HStack {
            Text(label)
                .foregroundColor(.secondary)
            Spacer()
            Text(value)
                .fontWeight(.medium)
        }
    }
}

// MARK: - 预览
struct MonitoringDashboardView_Previews: PreviewProvider {
    static var previews: some View {
        MonitoringDashboardView()
    }
} 