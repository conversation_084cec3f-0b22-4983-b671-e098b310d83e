//
//  MainView.swift
//  cop
//
//  Created by 阿亮 on 2025/5/31.
//

import SwiftUI
import UniformTypeIdentifiers

// MARK: - 侧边栏导航项
enum SidebarItem: String, CaseIterable {
    case mediaLibrary = "media_library"
    case folders = "folders"
    case webBrowser = "web_browser"
    case importManager = "import_manager"
    case settings = "settings"

    var title: String {
        switch self {
        case .mediaLibrary: return "媒体库"
        case .folders: return "库"
        case .webBrowser: return "网络浏览"
        case .importManager: return "导入管理"
        case .settings: return "应用管理"
        }
    }

    var icon: String {
        switch self {
        case .mediaLibrary: return "photo.on.rectangle.angled"
        case .folders: return "folder.fill"
        case .webBrowser: return "globe"
        case .importManager: return "square.and.arrow.down"
        case .settings: return "gear"
        }
    }
}

struct MainView: View {
    @StateObject private var viewModel = MediaLibraryViewModel()
    @State private var selectedSidebarItem: SidebarItem? = .mediaLibrary
    @State private var showingSidebar = false
    
    var body: some View {
        ZStack {
            // 主内容区域
            DetailContentView(
                selectedItem: selectedSidebarItem,
                viewModel: viewModel,
                showingSidebar: $showingSidebar,
                selectedSidebarItem: $selectedSidebarItem
            )
            
            // 浮动侧边栏遮罩和侧边栏
            if showingSidebar {
                Color.black.opacity(0.3)
                    .ignoresSafeArea()
                    .onTapGesture {
                        withAnimation(AppDesignSystem.Animation.standard) {
                            showingSidebar = false
                        }
                    }
                
                HStack {
                    ModernSidebarView(
                        selectedItem: $selectedSidebarItem,
                        showingSidebar: $showingSidebar,
                        viewModel: viewModel
                    )
                    .transition(.move(edge: .leading))
                    
                    Spacer()
                }
            }
        }
        .animation(AppDesignSystem.Animation.standard, value: showingSidebar)
    }
}

// MARK: - 现代化侧边栏
struct ModernSidebarView: View {
    @Binding var selectedItem: SidebarItem?
    @Binding var showingSidebar: Bool
    @ObservedObject var viewModel: MediaLibraryViewModel
    
    var body: some View {
        VStack(spacing: 0) {
            // 侧边栏头部
            SidebarHeader(showingSidebar: $showingSidebar)
            
            Divider()
                .foregroundColor(AppDesignSystem.Colors.separator)
            
            // 主导航菜单
            ScrollView {
                LazyVStack(spacing: AppDesignSystem.Spacing.xs) {
                    ForEach(SidebarItem.allCases, id: \.rawValue) { item in
                        ModernSidebarRow(
                            item: item,
                            isSelected: selectedItem == item
                        ) {
                            selectedItem = item
                            withAnimation(AppDesignSystem.Animation.standard) {
                                showingSidebar = false
                            }
                        }
                    }
                }
                .padding(.horizontal, AppDesignSystem.Spacing.md)
                .padding(.vertical, AppDesignSystem.Spacing.lg)
            }
            
            Spacer()
            
            // 侧边栏底部操作区域（抽屉样式优化）
            SidebarFooter(viewModel: viewModel)
        }
        .frame(width: AppDesignSystem.Layout.sidebarWidth)
        .frame(maxHeight: .infinity) // 确保侧边栏占满整个高度
        .background(
            // 毛玻璃效果
            .ultraThinMaterial
        )
        .overlay(
            // 抽屉样式边框（只在右侧添加边框）
            Rectangle()
                .fill(AppDesignSystem.Colors.separator.opacity(0.3))
                .frame(width: 1)
                .frame(maxWidth: .infinity, alignment: .trailing)
        )
        .ignoresSafeArea(.all, edges: .top) // 侧边栏顶部对齐，不留空
    }
}

// MARK: - 侧边栏头部
struct SidebarHeader: View {
    @Binding var showingSidebar: Bool
    @StateObject private var folderViewModel = FolderViewModel()
    
    var body: some View {
        HStack {
            VStack(alignment: .leading, spacing: AppDesignSystem.Spacing.xs) {
                Text("媒体管理器")
                    .font(AppDesignSystem.Typography.title3)
                    .fontWeight(.bold)
                    .foregroundColor(AppDesignSystem.Colors.text)
                
                Text("本地媒体文件管理")
                    .font(AppDesignSystem.Typography.caption1)
                    .foregroundColor(AppDesignSystem.Colors.textSecondary)
            }
            
            Spacer()
            
            // 如果在文件夹内容页面，显示返回按钮
            if folderViewModel.selectedFolder != nil {
                Button(action: {
                    folderViewModel.deselectFolder()
                }) {
                    Image(systemName: "arrow.left")
                        .font(.title2)
                        .foregroundColor(AppDesignSystem.Colors.iconSecondary)
                }
                .buttonStyle(ScaleButtonStyle())
            }
            
            Button(action: {
                withAnimation(AppDesignSystem.Animation.standard) {
                    showingSidebar = false
                }
            }) {
                Image(systemName: "sidebar.left")
                    .font(.title2)
                    .foregroundColor(AppDesignSystem.Colors.iconSecondary)
            }
            .buttonStyle(ScaleButtonStyle())
        }
        .padding(.horizontal, AppDesignSystem.Spacing.xl)
        .padding(.vertical, AppDesignSystem.Spacing.lg)
        .padding(.top, AppDesignSystem.Spacing.lg) // 增加顶部安全区域适配
    }
}

// MARK: - 现代化侧边栏行
struct ModernSidebarRow: View {
    let item: SidebarItem
    let isSelected: Bool
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            HStack(spacing: AppDesignSystem.Spacing.md) {
                Image(systemName: item.icon)
                    .font(.system(size: 20, weight: .medium))
                    .foregroundColor(isSelected ? .white : AppDesignSystem.Colors.icon)
                    .frame(width: 28, height: 28)
                
                Text(item.title)
                    .font(AppDesignSystem.Typography.bodyEmphasized)
                    .foregroundColor(isSelected ? .white : AppDesignSystem.Colors.text)
                
                Spacer()
                
                if isSelected {
                    Image(systemName: "chevron.right")
                        .font(.system(size: 14, weight: .medium))
                        .foregroundColor(.white.opacity(0.8))
                }
            }
            .padding(.horizontal, AppDesignSystem.Spacing.lg)
            .padding(.vertical, AppDesignSystem.Spacing.md)
            .background(
                RoundedRectangle(cornerRadius: AppDesignSystem.CornerRadius.md)
                    .fill(isSelected ? AppDesignSystem.Colors.primary : .clear)
            )
        }
        .buttonStyle(PlainButtonStyle())
        .scaleEffect(isSelected ? 1.02 : 1.0)
        .animation(AppDesignSystem.Animation.standard, value: isSelected)
    }
}

// MARK: - 侧边栏底部（优化后）
struct SidebarFooter: View {
    @ObservedObject var viewModel: MediaLibraryViewModel
    @State private var isRefreshing = false
    
    var body: some View {
        VStack(spacing: AppDesignSystem.Spacing.md) {
            Divider()
                .foregroundColor(AppDesignSystem.Colors.separator)
            
            // 扫描更新按钮（全宽设计）
            AppButton(
                isRefreshing ? "正在扫描..." : "扫描更新",
                icon: isRefreshing ? nil : "arrow.clockwise",
                style: .secondary,
                size: .medium
            ) {
                performRefresh()
            }
            .disabled(isRefreshing)
            .overlay(
                Group {
                    if isRefreshing {
                        HStack {
                            ProgressView()
                                .scaleEffect(0.8)
                                .progressViewStyle(CircularProgressViewStyle(tint: AppDesignSystem.Colors.primary))
                            Spacer()
                        }
                        .padding(.leading, AppDesignSystem.Spacing.lg)
                    }
                }
            )
            
            // 统计信息卡片
            if viewModel.allMediaFiles.count > 0 || viewModel.folders.count > 0 {
                VStack(spacing: AppDesignSystem.Spacing.sm) {
                    HStack {
                        StatInfoItem(
                            title: "媒体文件",
                            count: viewModel.allMediaFiles.count,
                            icon: "photo.on.rectangle"
                        )
                        
                        Spacer()
                        
                        StatInfoItem(
                            title: "文件夹",
                            count: viewModel.folders.count,
                            icon: "folder.fill"
                        )
                    }
                    
                    // 存储空间信息
                    let totalSize = calculateTotalSize()
                    if totalSize > 0 {
                        HStack {
                            Image(systemName: "internaldrive")
                                .font(.caption)
                                .foregroundColor(AppDesignSystem.Colors.iconSecondary)
                            
                            Text("总大小: \(formatFileSize(totalSize))")
                                .font(AppDesignSystem.Typography.caption2)
                                .foregroundColor(AppDesignSystem.Colors.textSecondary)
                            
                            Spacer()
                        }
                    }
                }
                .padding(.vertical, AppDesignSystem.Spacing.sm)
                .padding(.horizontal, AppDesignSystem.Spacing.md)
                .background(
                    RoundedRectangle(cornerRadius: AppDesignSystem.CornerRadius.sm)
                        .fill(AppDesignSystem.Colors.backgroundSecondary.opacity(0.5))
                )
            }
        }
        .padding(.horizontal, AppDesignSystem.Spacing.lg)
        .padding(.vertical, AppDesignSystem.Spacing.lg)
    }
    
    private func performRefresh() {
        withAnimation(AppDesignSystem.Animation.standard) {
            isRefreshing = true
        }
        
        Task {
            await viewModel.refreshAllMedia()
            await MainActor.run {
                withAnimation(AppDesignSystem.Animation.standard) {
                    isRefreshing = false
                }
            }
        }
    }
    
    private func calculateTotalSize() -> Int64 {
        return viewModel.folders.reduce(0) { $0 + $1.totalSize }
    }
    
    private func formatFileSize(_ size: Int64) -> String {
        ByteCountFormatter.string(fromByteCount: size, countStyle: .file)
    }
}

// MARK: - 统计信息项
struct StatInfoItem: View {
    let title: String
    let count: Int
    let icon: String
    
    var body: some View {
        VStack(alignment: .leading, spacing: AppDesignSystem.Spacing.xs) {
            HStack(spacing: AppDesignSystem.Spacing.xs) {
                Image(systemName: icon)
                    .font(.caption)
                    .foregroundColor(AppDesignSystem.Colors.iconSecondary)
                
                Text(title)
                    .font(AppDesignSystem.Typography.caption2)
                    .foregroundColor(AppDesignSystem.Colors.textSecondary)
            }
            
            Text("\(count)")
                .font(AppDesignSystem.Typography.headline)
                .fontWeight(.semibold)
                .foregroundColor(AppDesignSystem.Colors.text)
        }
    }
}

// MARK: - 详情内容视图
struct DetailContentView: View {
    let selectedItem: SidebarItem?
    @ObservedObject var viewModel: MediaLibraryViewModel
    @Binding var showingSidebar: Bool
    @Binding var selectedSidebarItem: SidebarItem?
    
    // 共享的FolderViewModel实例
    @StateObject private var folderViewModel = FolderViewModel()
    // 使用环境对象中的NewWebBrowserViewModel
    @EnvironmentObject var browserViewModel: NewWebBrowserViewModel
    
    var body: some View {
        // 网络浏览器页面特殊处理：不显示app导航栏
        if selectedItem == .webBrowser {
            // 使用新的浏览器实现
            NewWebBrowserView(
                onToggleSidebar: {
                    withAnimation(AppDesignSystem.Animation.standard) {
                        showingSidebar.toggle()
                    }
                }
            )
            .frame(maxWidth: .infinity, maxHeight: .infinity)
            .background(AppDesignSystem.Colors.groupedBackground)
        } else {
            VStack(spacing: 0) {
                // 统一的顶部导航栏（网络浏览器除外）
                ModernNavigationBar(
                    selectedItem: selectedItem,
                    showingSidebar: $showingSidebar,
                    folderViewModel: folderViewModel
                )

                // 主内容区域
                Group {
                    switch selectedItem {
                    case .mediaLibrary:
                        MediaLibraryDetailView(viewModel: viewModel, selectedSidebarItem: $selectedSidebarItem)
                    case .folders:
                        // 传递共享的folderViewModel实例
                        ModernFolderView(selectedSidebarItem: $selectedSidebarItem, folderViewModel: folderViewModel)
                    case .importManager:
                        ImportProgressView(viewModel: viewModel)
                    case .settings:
                        SettingsDetailView(viewModel: viewModel)
                    case .webBrowser:
                        // 这个case不会被执行，因为已经在上面处理了
                        EmptyView()
                    case .none:
                        AppEmptyStateView(
                            icon: "questionmark.circle",
                            title: "选择功能",
                            description: "请从侧边栏选择要使用的功能"
                        )
                    }
                }
                .frame(maxWidth: .infinity, maxHeight: .infinity)
                .background(AppDesignSystem.Colors.groupedBackground)
            }
        }
    }
}

// MARK: - 现代化导航栏
struct ModernNavigationBar: View {
    let selectedItem: SidebarItem?
    @Binding var showingSidebar: Bool
    @ObservedObject var folderViewModel: FolderViewModel
    @EnvironmentObject var newWebBrowserViewModel: NewWebBrowserViewModel
    
    var body: some View {
        HStack {
            // 侧边栏切换按钮
            Button(action: {
                withAnimation(AppDesignSystem.Animation.standard) {
                    showingSidebar.toggle()
                }
            }) {
                Image(systemName: "sidebar.left")
                    .font(.system(size: 20, weight: .medium))
                    .foregroundColor(AppDesignSystem.Colors.icon)
                    .frame(width: 44, height: 44)
            }
            .buttonStyle(ScaleButtonStyle())
            
            // 文件夹返回按钮（当在文件夹内容页面时显示）
            if selectedItem == .folders && folderViewModel.selectedFolder != nil {
                Button(action: {
                    folderViewModel.deselectFolder()
                }) {
                    Image(systemName: "chevron.left")
                        .font(.system(size: 20, weight: .medium))
                        .foregroundColor(AppDesignSystem.Colors.icon)
                        .frame(width: 44, height: 44)
                }
                .buttonStyle(ScaleButtonStyle())
            }
            
            Spacer()
            
            // 页面标题（动态显示）
            Text(dynamicTitle)
                .font(AppDesignSystem.Typography.navigationTitle)
                .foregroundColor(AppDesignSystem.Colors.text)
            
            Spacer()
            
            // 右侧功能按钮
            HStack(spacing: 0) {
                if selectedItem == .folders && folderViewModel.selectedFolder != nil {
                    // 文件夹页面的功能按钮
                    Menu {
                        Button("重新扫描", action: {
                            Task {
                                if let folder = folderViewModel.selectedFolder {
                                    await folderViewModel.rescanFolder(folder)
                                }
                            }
                        })
                        Button("导出文件夹", action: {
                            // 导出功能
                        })
                    } label: {
                        Image(systemName: "ellipsis.circle")
                            .font(.system(size: 20, weight: .medium))
                            .foregroundColor(AppDesignSystem.Colors.icon)
                            .frame(width: 44, height: 44)
                    }
                } else {
                    // 占位按钮组合，确保标题居中
                    if selectedItem == .folders && folderViewModel.selectedFolder != nil {
                        // 当显示返回按钮时，需要额外的占位空间
                        Color.clear
                            .frame(width: 44, height: 44)
                    }
                    if selectedItem == .webBrowser {
                        // 新建标签页按钮占位
                        Color.clear
                            .frame(width: 44, height: 44)
                    }
                    Color.clear
                        .frame(width: 44, height: 44)
                }
            }
        }
        .padding(.horizontal, AppDesignSystem.Spacing.xl)
        .padding(.vertical, AppDesignSystem.Spacing.lg)
        .frame(minHeight: AppDesignSystem.Spacing.toolbarHeight)
        .background(AppDesignSystem.Colors.material)
        // 新的浏览器有自己的设置、历史记录和书签管理
    }
    
    private var dynamicTitle: String {
        switch selectedItem {
        case .folders:
            if let folderName = folderViewModel.selectedFolder?.name {
                return folderName
            } else {
                return "库"
            }
        default:
            return selectedItem?.title ?? "媒体管理器"
        }
    }
}

// MARK: - 媒体库详情视图
struct MediaLibraryDetailView: View {
    @ObservedObject var viewModel: MediaLibraryViewModel
    @Binding var selectedSidebarItem: SidebarItem?
    
    var body: some View {
        VStack(spacing: 0) {
            // 媒体库工具栏
            MediaLibraryModernToolbar(viewModel: viewModel)
            
            // 主内容区域
            if viewModel.isLoading {
                AppLoadingView("正在加载媒体文件...", style: .skeleton)
            } else if viewModel.allMediaFiles.isEmpty {
                AppEmptyStateView(
                    icon: "photo.on.rectangle.angled",
                    title: "暂无媒体文件",
                    description: "导入文件夹以开始管理您的媒体文件",
                    actionTitle: "导入文件夹"
                ) {
                    // 跳转到导入管理页面
                    selectedSidebarItem = .importManager
                }
            } else {
                MediaLibraryContentView(viewModel: viewModel)
            }
            
            // 底部提示信息
            if let errorMessage = viewModel.errorMessage {
                AppCard {
                    HStack {
                        Image(systemName: "info.circle")
                            .foregroundColor(AppDesignSystem.Colors.warning)
                        Text(errorMessage)
                            .font(AppDesignSystem.Typography.caption1)
                            .foregroundColor(AppDesignSystem.Colors.textSecondary)
                        Spacer()
                        AppButton("关闭", style: .tertiary, size: .small) {
                            viewModel.clearError()
                        }
                    }
                }
                .padding(.horizontal, AppDesignSystem.Spacing.lg)
                .transition(.move(edge: .bottom))
            }
        }
    }
}

// MARK: - 现代化工具栏
struct MediaLibraryModernToolbar: View {
    @ObservedObject var viewModel: MediaLibraryViewModel
    
    var body: some View {
        VStack(spacing: AppDesignSystem.Spacing.md) {
            // 主工具行
            HStack(spacing: AppDesignSystem.Spacing.md) {
                AppSearchField("搜索媒体文件...", text: $viewModel.searchText)
                    .frame(maxWidth: .infinity)
                
                MediaFilterMenu(viewModel: viewModel)
                SortMenu(viewModel: viewModel)
                ViewModeMenu(viewModel: viewModel)
            }
        }
        .padding(.horizontal, AppDesignSystem.Spacing.xl)
        .padding(.vertical, AppDesignSystem.Spacing.lg)
        .frame(minHeight: AppDesignSystem.Spacing.toolbarHeight)
        .background(AppDesignSystem.Colors.material)
    }
}

// MARK: - 媒体过滤菜单（优化显示状态）
struct MediaFilterMenu: View {
    @ObservedObject var viewModel: MediaLibraryViewModel
    
    var body: some View {
        Menu {
            Section("媒体类型") {
                Button(action: { viewModel.selectedMediaType = nil }) {
                    HStack {
                        Text("全部")
                        Spacer()
                        if viewModel.selectedMediaType == nil {
                            Image(systemName: "checkmark")
                                .foregroundColor(AppDesignSystem.Colors.primary)
                        }
                    }
                }
                
                Button(action: { viewModel.selectedMediaType = .image }) {
                    HStack {
                        Text("图片")
                        Spacer()
                        if viewModel.selectedMediaType == .image {
                            Image(systemName: "checkmark")
                                .foregroundColor(AppDesignSystem.Colors.primary)
                        }
                    }
                }
                
                Button(action: { viewModel.selectedMediaType = .video }) {
                    HStack {
                        Text("视频")
                        Spacer()
                        if viewModel.selectedMediaType == .video {
                            Image(systemName: "checkmark")
                                .foregroundColor(AppDesignSystem.Colors.primary)
                        }
                    }
                }
            }
        } label: {
            HStack(spacing: 6) {
                Image(systemName: filterIcon)
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(isFilterActive ? AppDesignSystem.Colors.primary : AppDesignSystem.Colors.icon)
                
                Text(isFilterActive ? filterLabel : "筛选")
                    .font(.system(size: 14, weight: .medium))
                    .foregroundColor(isFilterActive ? AppDesignSystem.Colors.primary : AppDesignSystem.Colors.icon)
            }
            .padding(.horizontal, 12)
            .padding(.vertical, 8)
            .background(
                RoundedRectangle(cornerRadius: 8)
                    .fill(isFilterActive ? AppDesignSystem.Colors.primary.opacity(0.1) : Color.clear)
            )
        }
    }
    
    private var isFilterActive: Bool {
        viewModel.selectedMediaType != nil
    }
    
    private var filterIcon: String {
        switch viewModel.selectedMediaType {
        case .image: return "photo"
        case .video: return "video"
        case .none: return "line.3.horizontal.decrease.circle"
        }
    }
    
    private var filterLabel: String {
        switch viewModel.selectedMediaType {
        case .image: return "图片"
        case .video: return "视频"
        case .none: return ""
        }
    }
}

// MARK: - 排序菜单（优化组合方式和方向）
struct SortMenu: View {
    @ObservedObject var viewModel: MediaLibraryViewModel
    
    var body: some View {
        Menu {
            Section("排序方式") {
                ForEach(MediaSortOption.allCases, id: \.self) { option in
                    Button(action: { 
                        if viewModel.sortOption == option {
                            // 如果是同一个选项，切换排序方向
                            viewModel.sortDirection = viewModel.sortDirection == .ascending ? .descending : .ascending
                        } else {
                            viewModel.sortOption = option
                        }
                    }) {
                        HStack {
                            Text(option.displayName)
                            Spacer()
                            
                            if viewModel.sortOption == option {
                                HStack(spacing: 4) {
                                    Image(systemName: viewModel.sortDirection == .ascending ? "arrow.up" : "arrow.down")
                                        .foregroundColor(AppDesignSystem.Colors.primary)
                                    Image(systemName: "checkmark")
                                        .foregroundColor(AppDesignSystem.Colors.primary)
                                }
                            }
                        }
                    }
                }
            }
        } label: {
            HStack(spacing: 6) {
                Image(systemName: sortIcon)
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(AppDesignSystem.Colors.primary)
                
                Text("\(sortLabel) \(sortDirection)")
                    .font(.system(size: 14, weight: .medium))
                    .foregroundColor(AppDesignSystem.Colors.primary)
            }
            .padding(.horizontal, 12)
            .padding(.vertical, 8)
            .background(
                RoundedRectangle(cornerRadius: 8)
                    .fill(AppDesignSystem.Colors.primary.opacity(0.1))
            )
        }
    }
    
    private var sortIcon: String {
        viewModel.sortDirection == .ascending ? "arrow.up.circle.fill" : "arrow.down.circle.fill"
    }
    
    private var sortLabel: String {
        switch viewModel.sortOption {
        case .name: return "名称"
        case .dateCreated: return "创建"
        case .dateModified: return "修改"
        case .fileSize: return "大小"
        case .type: return "类型"
        }
    }
    
    private var sortDirection: String {
        viewModel.sortDirection == .ascending ? "↑" : "↓"
    }
}

// MARK: - 视图模式菜单
struct ViewModeMenu: View {
    @ObservedObject var viewModel: MediaLibraryViewModel
    
    var body: some View {
        Menu {
            Section("视图模式") {
                Button(action: { viewModel.setViewMode(.grid) }) {
                    HStack {
                        Text("网格视图")
                        Spacer()
                        if viewModel.viewMode == .grid {
                            Image(systemName: "checkmark")
                                .foregroundColor(AppDesignSystem.Colors.primary)
                        }
                    }
                }
                
                Button(action: { viewModel.setViewMode(.list) }) {
                    HStack {
                        Text("列表视图")
                        Spacer()
                        if viewModel.viewMode == .list {
                            Image(systemName: "checkmark")
                                .foregroundColor(AppDesignSystem.Colors.primary)
                        }
                    }
                }
                
                Button(action: { viewModel.setViewMode(.waterfall) }) {
                    HStack {
                        Text("瀑布视图")
                        Spacer()
                        if viewModel.viewMode == .waterfall {
                            Image(systemName: "checkmark")
                                .foregroundColor(AppDesignSystem.Colors.primary)
                        }
                    }
                }
            }
        } label: {
            HStack(spacing: 6) {
                Image(systemName: viewModeIcon)
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(AppDesignSystem.Colors.icon)
                
                Text(viewModeLabel)
                    .font(.system(size: 14, weight: .medium))
                    .foregroundColor(AppDesignSystem.Colors.icon)
            }
            .padding(.horizontal, 12)
            .padding(.vertical, 8)
            .background(
                RoundedRectangle(cornerRadius: 8)
                    .fill(Color.clear)
            )
        }
    }
    
    private var viewModeIcon: String {
        switch viewModel.viewMode {
        case .grid: return "square.grid.2x2"
        case .list: return "list.bullet"
        case .waterfall: return "rectangle.grid.1x2"
        }
    }
    
    private var viewModeLabel: String {
        switch viewModel.viewMode {
        case .grid: return "网格"
        case .list: return "列表"
        case .waterfall: return "瀑布"
        }
    }
}

// MARK: - 媒体库内容视图
struct MediaLibraryContentView: View {
    @ObservedObject var viewModel: MediaLibraryViewModel

    var body: some View {
        Group {
            switch viewModel.viewMode {
            case .grid:
                ModernGridBrowser(mediaFiles: viewModel.displayedMediaFiles, viewModel: viewModel)
            case .list:
                ModernListBrowser(mediaFiles: viewModel.displayedMediaFiles, viewModel: viewModel)
            case .waterfall:
                ModernWaterfallBrowser(mediaFiles: viewModel.displayedMediaFiles, viewModel: viewModel)
            }
        }
        .fullScreenCover(item: $viewModel.fullScreenMediaItem) { item in
            FullScreenMediaViewer(
                mediaFiles: item.mediaFiles,
                initialIndex: item.initialIndex,
                viewModel: viewModel,
                initialUIState: item.initialUIState
            )
        }
    }
}

// MARK: - 设置详情视图
struct SettingsDetailView: View {
    @ObservedObject var viewModel: MediaLibraryViewModel
    
    var body: some View {
        SettingsView(viewModel: viewModel)
    }
}

// MARK: - 网格浏览视图（保持兼容性）
struct GridBrowserView: View {
    @ObservedObject var viewModel: MediaLibraryViewModel
    
    var body: some View {
        ModernGridBrowser(mediaFiles: viewModel.displayedMediaFiles, viewModel: viewModel)
    }
}

// MARK: - 列表浏览视图（保持兼容性）
struct ListBrowserView: View {
    @ObservedObject var viewModel: MediaLibraryViewModel
    
    var body: some View {
        ModernListBrowser(mediaFiles: viewModel.displayedMediaFiles, viewModel: viewModel)
    }
}

// MARK: - 瀑布流浏览视图（保持兼容性）
struct WaterfallBrowserView: View {
    @ObservedObject var viewModel: MediaLibraryViewModel
    
    var body: some View {
        ModernWaterfallBrowser(mediaFiles: viewModel.displayedMediaFiles, viewModel: viewModel)
    }
}

// MARK: - 预览
#Preview {
    MainView()
} 